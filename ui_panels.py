"""
UI面板模块 - 包含各个功能面板类
"""

import json
import csv
import os
from datetime import datetime
from typing import Dict, List, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox, 
    QLabel, QLineEdit, QSpinBox, QPushButton, QCheckBox, QComboBox,
    QProgressBar, QTextEdit, QTabWidget, QTableWidget, QTableWidgetItem,
    QHeaderView, QFrame, QScrollArea, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from ui_components import (
    ModernButton, get_group_style, get_spinbox_style, 
    get_checkbox_style, get_line_edit_style, get_combo_box_style
)
from utils import (
    load_config, json_to_csv, get_douyin_ids_from_config, 
    is_batch_mode_enabled, get_data_file_path
)


class ConfigPanel(QWidget):
    """配置面板"""
    def __init__(self):
        super().__init__()
        self.config = load_config()
        self.init_ui()
        self.load_config_values()
    
    def get_group_style(self):
        """获取分组样式"""
        return get_group_style()
    
    def get_spinbox_style(self):
        """获取数字输入框样式"""
        return get_spinbox_style()
    
    def get_checkbox_style(self):
        """获取复选框样式"""
        return get_checkbox_style()
    
    def init_ui(self):
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 标题
        title = QLabel("配置设置")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px; 
                font-weight: bold; 
                color: #1f2937; 
                padding: 20px;
                background-color: #f8fafc;
                border-bottom: 1px solid #e2e8f0;
            }
        """)
        main_layout.addWidget(title)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #f8fafc;
            }
            QScrollBar:vertical {
                background-color: #f1f5f9;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #cbd5e1;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #94a3b8;
            }
        """)
        
        # 滚动内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(20)
        scroll_layout.setContentsMargins(20, 20, 20, 20)
        
        # 基础配置组
        self.create_basic_config_group(scroll_layout)
        
        # 抓取限制组
        self.create_limits_group(scroll_layout)
        
        # 性能优化组
        self.create_optimization_group(scroll_layout)
        
        # 批量处理设置组
        self.create_batch_config_group(scroll_layout)
        
        # 导出设置组
        self.create_export_group(scroll_layout)
        
        # 添加弹性空间，让内容顶部对齐
        scroll_layout.addStretch()
        
        # 保存配置按钮
        save_btn = ModernButton("保存配置", "success")
        save_btn.clicked.connect(self.save_config)
        scroll_layout.addWidget(save_btn)
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
    
    def create_basic_config_group(self, parent_layout):
        """创建基础配置组"""
        group = QGroupBox("基础配置")
        group.setStyleSheet(self.get_group_style())
        
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # 批量模式开关
        batch_mode_layout = QHBoxLayout()
        batch_mode_layout.setSpacing(10)
        
        self.enable_batch_mode = QCheckBox("启用批量处理模式")
        self.enable_batch_mode.setStyleSheet(self.get_checkbox_style())
        self.enable_batch_mode.toggled.connect(self.on_batch_mode_toggled)
        batch_mode_layout.addWidget(self.enable_batch_mode)
        
        batch_mode_layout.addStretch()
        layout.addLayout(batch_mode_layout)
        
        # 抖音ID输入区域
        id_layout = QVBoxLayout()
        id_layout.setSpacing(5)
        
        # 单个ID输入（兼容旧模式）
        self.single_id_label = QLabel("目标抖音ID:")
        id_layout.addWidget(self.single_id_label)
        
        self.douyin_id_input = QLineEdit()
        self.douyin_id_input.setPlaceholderText("请输入要抓取的抖音用户ID")
        self.douyin_id_input.setMinimumHeight(35)
        self.douyin_id_input.setStyleSheet(get_line_edit_style())
        id_layout.addWidget(self.douyin_id_input)
        
        # 批量ID输入（新功能）
        self.batch_id_label = QLabel("批量抖音ID列表（每行一个ID）:")
        id_layout.addWidget(self.batch_id_label)
        
        self.batch_id_input = QTextEdit()
        self.batch_id_input.setPlaceholderText("每行输入一个抖音ID，例如：\n96967475948\n123456789\n987654321")
        self.batch_id_input.setMaximumHeight(120)
        self.batch_id_input.setStyleSheet("""
            QTextEdit {
                padding: 8px 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
                font-family: 'Consolas', 'Monaco', monospace;
            }
            QTextEdit:focus {
                border-color: #2563eb;
                outline: none;
            }
        """)
        id_layout.addWidget(self.batch_id_input)
        
        layout.addLayout(id_layout)
        
        # 参数设置 - 使用更好的水平布局
        params_widget = QWidget()
        params_layout = QGridLayout(params_widget)
        params_layout.setSpacing(20)
        params_layout.setColumnStretch(1, 1)  # 让输入框列可以拉伸
        params_layout.setColumnStretch(3, 1)  # 让第二列输入框也可以拉伸
        
        # 第一行：JS执行超时 | 重试次数
        params_layout.addWidget(QLabel("JS执行超时(秒):"), 0, 0)
        self.js_timeout_input = QSpinBox()
        self.js_timeout_input.setRange(3, 60)
        self.js_timeout_input.setValue(10)
        self.js_timeout_input.setMinimumHeight(35)
        self.js_timeout_input.setMinimumWidth(100)
        self.js_timeout_input.setStyleSheet(self.get_spinbox_style())
        params_layout.addWidget(self.js_timeout_input, 0, 1)
        
        params_layout.addWidget(QLabel("重试次数:"), 0, 2)
        self.js_retry_input = QSpinBox()
        self.js_retry_input.setRange(1, 10)
        self.js_retry_input.setValue(3)
        self.js_retry_input.setMinimumHeight(35)
        self.js_retry_input.setMinimumWidth(100)
        self.js_retry_input.setStyleSheet(self.get_spinbox_style())
        params_layout.addWidget(self.js_retry_input, 0, 3)
        
        # 添加一些列间距
        params_layout.setColumnMinimumWidth(0, 150)  # 标签列最小宽度
        params_layout.setColumnMinimumWidth(2, 150)  # 第二个标签列最小宽度
        
        layout.addWidget(params_widget)
        parent_layout.addWidget(group)
    
    def create_limits_group(self, parent_layout):
        """创建抓取限制组"""
        group = QGroupBox("抓取限制")
        group.setStyleSheet(self.get_group_style())
        
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # 使用水平布局放置限制设置
        limits_widget = QWidget()
        limits_layout = QGridLayout(limits_widget)
        limits_layout.setSpacing(20)
        limits_layout.setColumnStretch(1, 1)
        limits_layout.setColumnStretch(3, 1)
        
        # 第一行：最大粉丝数量 | 最大点赞数量
        limits_layout.addWidget(QLabel("最大粉丝数量:"), 0, 0)
        self.max_follower_input = QSpinBox()
        self.max_follower_input.setRange(1, 10000)
        self.max_follower_input.setValue(30)
        self.max_follower_input.setMinimumHeight(35)
        self.max_follower_input.setMinimumWidth(100)
        self.max_follower_input.setStyleSheet(self.get_spinbox_style())
        limits_layout.addWidget(self.max_follower_input, 0, 1)
        
        limits_layout.addWidget(QLabel("最大点赞数量:"), 0, 2)
        self.max_favorite_input = QSpinBox()
        self.max_favorite_input.setRange(1, 10000)
        self.max_favorite_input.setValue(20)
        self.max_favorite_input.setMinimumHeight(35)
        self.max_favorite_input.setMinimumWidth(100)
        self.max_favorite_input.setStyleSheet(self.get_spinbox_style())
        limits_layout.addWidget(self.max_favorite_input, 0, 3)
        
        # 设置列宽
        limits_layout.setColumnMinimumWidth(0, 150)  # 标签列最小宽度
        limits_layout.setColumnMinimumWidth(2, 150)  # 第二个标签列最小宽度
        
        layout.addWidget(limits_widget)
        parent_layout.addWidget(group)
    
    def create_optimization_group(self, parent_layout):
        """创建性能优化组"""
        group = QGroupBox("性能优化")
        group.setStyleSheet(self.get_group_style())
        
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # 优化阶段选择
        opt_layout = QHBoxLayout()
        opt_layout.addWidget(QLabel("优化阶段:"))
        
        self.optimization_stage = QComboBox()
        self.optimization_stage.addItems(["阶段1(监听器复用)", "阶段2(+队列清理)", "阶段3(完整优化)"])
        self.optimization_stage.setCurrentIndex(2)  # 默认阶段3
        self.optimization_stage.setStyleSheet(get_combo_box_style())
        opt_layout.addWidget(self.optimization_stage)
        opt_layout.addStretch()
        
        layout.addLayout(opt_layout)
        
        # 并发设置
        concurrent_layout = QVBoxLayout()
        
        self.enable_concurrent = QCheckBox("启用并发模式")
        self.enable_concurrent.setStyleSheet(self.get_checkbox_style())
        concurrent_layout.addWidget(self.enable_concurrent)
        
        tabs_layout = QHBoxLayout()
        tabs_layout.addWidget(QLabel("最大并发标签页:"))
        
        self.max_concurrent_tabs = QSpinBox()
        self.max_concurrent_tabs.setRange(1, 10)
        self.max_concurrent_tabs.setValue(3)
        self.max_concurrent_tabs.setStyleSheet(self.get_spinbox_style())
        tabs_layout.addWidget(self.max_concurrent_tabs)
        tabs_layout.addStretch()
        
        concurrent_layout.addLayout(tabs_layout)
        layout.addLayout(concurrent_layout)
        
        parent_layout.addWidget(group)
    
    def create_batch_config_group(self, parent_layout):
        """创建批量处理设置组"""
        group = QGroupBox("批量处理设置")
        group.setStyleSheet(self.get_group_style())
        
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # 批量参数设置 - 使用水平布局
        batch_params_widget = QWidget()
        batch_params_layout = QGridLayout(batch_params_widget)
        batch_params_layout.setSpacing(20)
        batch_params_layout.setColumnStretch(1, 1)
        batch_params_layout.setColumnStretch(3, 1)
        
        # 第一行：批量间隔时间 | ID重试次数
        batch_params_layout.addWidget(QLabel("批量间隔时间(秒):"), 0, 0)
        self.batch_interval = QSpinBox()
        self.batch_interval.setRange(1, 60)
        self.batch_interval.setValue(5)
        self.batch_interval.setMinimumHeight(35)
        self.batch_interval.setMinimumWidth(100)
        self.batch_interval.setStyleSheet(self.get_spinbox_style())
        batch_params_layout.addWidget(self.batch_interval, 0, 1)
        
        batch_params_layout.addWidget(QLabel("ID重试次数:"), 0, 2)
        self.id_retry_count = QSpinBox()
        self.id_retry_count.setRange(1, 10)
        self.id_retry_count.setValue(2)
        self.id_retry_count.setMinimumHeight(35)
        self.id_retry_count.setMinimumWidth(100)
        self.id_retry_count.setStyleSheet(self.get_spinbox_style())
        batch_params_layout.addWidget(self.id_retry_count, 0, 3)
        
        # 设置列宽
        batch_params_layout.setColumnMinimumWidth(0, 150)
        batch_params_layout.setColumnMinimumWidth(2, 150)
        
        layout.addWidget(batch_params_widget)
        
        # 批量选项 - 使用水平布局
        batch_options_layout = QHBoxLayout()
        batch_options_layout.setSpacing(30)
        
        # 跳过失败ID
        self.skip_failed_ids = QCheckBox("跳过失败的ID继续处理")
        self.skip_failed_ids.setChecked(True)
        self.skip_failed_ids.setStyleSheet(self.get_checkbox_style())
        batch_options_layout.addWidget(self.skip_failed_ids)
        
        # 生成批量报告
        self.generate_batch_report = QCheckBox("生成批量处理报告")
        self.generate_batch_report.setChecked(True)
        self.generate_batch_report.setStyleSheet(self.get_checkbox_style())
        batch_options_layout.addWidget(self.generate_batch_report)
        
        batch_options_layout.addStretch()  # 添加弹性空间
        
        layout.addLayout(batch_options_layout)
        parent_layout.addWidget(group)
    
    def create_export_group(self, parent_layout):
        """创建导出设置组"""
        group = QGroupBox("导出设置")
        group.setStyleSheet(self.get_group_style())
        
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # 导出格式选择 - 使用水平布局
        format_layout = QHBoxLayout()
        format_layout.setSpacing(30)
        
        format_layout.addWidget(QLabel("导出格式:"))
        
        self.export_json = QCheckBox("JSON")
        self.export_json.setChecked(True)
        self.export_json.setStyleSheet(self.get_checkbox_style())
        format_layout.addWidget(self.export_json)
        
        self.export_csv = QCheckBox("CSV")
        self.export_csv.setChecked(True)
        self.export_csv.setStyleSheet(self.get_checkbox_style())
        format_layout.addWidget(self.export_csv)
        
        format_layout.addStretch()  # 添加弹性空间
        
        layout.addLayout(format_layout)
        parent_layout.addWidget(group)
    
    def on_batch_mode_toggled(self, enabled: bool):
        """切换批量模式时的处理"""
        # 控制不同输入区域的显示
        self.single_id_label.setVisible(not enabled)
        self.douyin_id_input.setVisible(not enabled)
        self.batch_id_label.setVisible(enabled) 
        self.batch_id_input.setVisible(enabled)
    
    def load_config_values(self):
        """从配置文件加载值"""
        try:
            # 基础配置
            scraper_config = self.config.get('scraper', {})
            self.js_timeout_input.setValue(scraper_config.get('js_timeout', 10))
            self.js_retry_input.setValue(scraper_config.get('js_retry', 3))
            
            # 限制配置
            limits_config = self.config.get('limits', {})
            self.max_follower_input.setValue(limits_config.get('max_follower_count', 30))
            self.max_favorite_input.setValue(limits_config.get('max_favorite_count', 20))
            
            # 优化配置
            optimization_config = self.config.get('optimization', {})
            opt_stage = optimization_config.get('optimization_stage', 3)
            self.optimization_stage.setCurrentIndex(opt_stage - 1)
            
            # 并发配置
            concurrent_config = self.config.get('concurrent', {})
            self.enable_concurrent.setChecked(concurrent_config.get('enable_concurrent_mode', True))
            self.max_concurrent_tabs.setValue(concurrent_config.get('max_concurrent_tabs', 3))
            
            # 批量配置
            batch_config = self.config.get('batch', {})
            self.batch_interval.setValue(batch_config.get('batch_interval_seconds', 5))
            self.id_retry_count.setValue(batch_config.get('id_retry_count', 2))
            self.skip_failed_ids.setChecked(batch_config.get('skip_failed_ids', True))
            self.generate_batch_report.setChecked(batch_config.get('generate_batch_report', True))
            
            # 批量模式
            douyin_config = self.config.get('douyin_id', {})
            batch_enabled = douyin_config.get('enable_batch_mode', False)
            self.enable_batch_mode.setChecked(batch_enabled)
            self.on_batch_mode_toggled(batch_enabled)
            
            # 加载抖音ID
            if batch_enabled:
                douyin_ids = douyin_config.get('douyin_ids', [])
                self.batch_id_input.setPlainText('\n'.join(str(id_) for id_ in douyin_ids))
            else:
                # 非批量模式下，从douyin_ids列表中取第一个ID，保持向后兼容
                douyin_ids = douyin_config.get('douyin_ids', [])
                if douyin_ids:
                    self.douyin_id_input.setText(str(douyin_ids[0]))
                else:
                    # 兼容旧配置格式
                    single_id = douyin_config.get('douyin_id', '')
                    self.douyin_id_input.setText(str(single_id) if single_id else '')
                
        except Exception as e:
            print(f"加载配置时出错: {e}")
    
    def get_config_dict(self) -> Dict:
        """获取当前配置"""
        # 获取抖音ID列表
        if self.enable_batch_mode.isChecked():
            douyin_ids = [line.strip() for line in self.batch_id_input.toPlainText().split('\n') if line.strip()]
        else:
            douyin_ids = [self.douyin_id_input.text().strip()] if self.douyin_id_input.text().strip() else []
        
        return {
            'douyin_ids': douyin_ids,
            'enable_batch_mode': self.enable_batch_mode.isChecked(),
            'js_timeout': self.js_timeout_input.value(),
            'js_retry': self.js_retry_input.value(),
            'max_follower_count': self.max_follower_input.value(),
            'max_favorite_count': self.max_favorite_input.value(),
            'optimization_stage': self.optimization_stage.currentIndex() + 1,
            'enable_concurrent_mode': self.enable_concurrent.isChecked(),
            'max_concurrent_tabs': self.max_concurrent_tabs.value(),
            'batch_interval_seconds': self.batch_interval.value(),
            'id_retry_count': self.id_retry_count.value(),
            'skip_failed_ids': self.skip_failed_ids.isChecked(),
            'generate_batch_report': self.generate_batch_report.isChecked(),
            'export_json': self.export_json.isChecked(),
            'export_csv': self.export_csv.isChecked()
        }
    
    def save_config(self):
        """保存配置到文件"""
        try:
            config_dict = self.get_config_dict()
            
            # 构建完整配置结构
            full_config = {
                'douyin_id': {
                    'enable_batch_mode': config_dict['enable_batch_mode'],
                    'douyin_ids': config_dict['douyin_ids']
                },
                'scraper': {
                    'js_timeout': config_dict['js_timeout'],
                    'js_retry': config_dict['js_retry']
                },
                'limits': {
                    'max_follower_count': config_dict['max_follower_count'],
                    'max_favorite_count': config_dict['max_favorite_count']
                },
                'optimization': {
                    'optimization_stage': config_dict['optimization_stage']
                },
                'concurrent': {
                    'enable_concurrent_mode': config_dict['enable_concurrent_mode'],
                    'max_concurrent_tabs': config_dict['max_concurrent_tabs']
                },
                'batch': {
                    'batch_interval_seconds': config_dict['batch_interval_seconds'],
                    'id_retry_count': config_dict['id_retry_count'],
                    'skip_failed_ids': config_dict['skip_failed_ids'],
                    'generate_batch_report': config_dict['generate_batch_report']
                }
            }
            
            # 保存到文件
            import toml
            config_path = 'config.toml'
            with open(config_path, 'w', encoding='utf-8') as f:
                toml.dump(full_config, f)
            
            QMessageBox.information(self, "成功", "配置已保存成功！")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")


# 由于文件过大，我将分别创建其他面板类的定义
# 这里先占位，后续会添加完整的类定义

class ControlPanel(QWidget):
    """功能控制面板"""
    
    # 信号定义
    start_scraping = pyqtSignal(list)  # 开始抓取信号
    stop_scraping = pyqtSignal()       # 停止抓取信号
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("抓取控制")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # 抓取类型选择组
        self.create_scrape_type_group(layout)
        
        # 控制按钮组
        self.create_control_buttons(layout)
        
        # 添加弹性空间
        layout.addStretch()
    
    def create_scrape_type_group(self, parent_layout):
        """创建抓取类型选择组"""
        group = QGroupBox("抓取类型")
        group.setStyleSheet(get_group_style())
        
        layout = QVBoxLayout(group)
        layout.setSpacing(8)
        
        # 抓取类型复选框
        self.scrape_user_profile = QCheckBox("用户基本信息")
        self.scrape_followers = QCheckBox("粉丝列表")
        self.scrape_favorites = QCheckBox("喜欢列表")

        # 默认选中
        self.scrape_user_profile.setChecked(True)
        self.scrape_followers.setChecked(True)
        self.scrape_favorites.setChecked(True)

        # 只添加显示的复选框到布局
        for checkbox in [self.scrape_user_profile, self.scrape_followers, self.scrape_favorites]:
            checkbox.setStyleSheet(get_checkbox_style() + """
                QCheckBox {
                    font-size: 14px;
                    spacing: 8px;
                }
            """)
            layout.addWidget(checkbox)
        
        parent_layout.addWidget(group)
    
    def create_control_buttons(self, parent_layout):
        """创建控制按钮组"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 开始按钮
        self.start_btn = ModernButton("开始抓取", "success")
        self.start_btn.clicked.connect(self.on_start_clicked)
        button_layout.addWidget(self.start_btn)
        
        # 停止按钮
        self.stop_btn = ModernButton("停止抓取", "danger")
        self.stop_btn.clicked.connect(self.on_stop_clicked)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        parent_layout.addLayout(button_layout)
    
    def on_start_clicked(self):
        """开始按钮点击事件"""
        selected_types = []
        
        if self.scrape_user_profile.isChecked():
            selected_types.append("user_profile")
        if self.scrape_followers.isChecked():
            selected_types.append("followers")
        if self.scrape_favorites.isChecked():
            selected_types.append("favorites")
        
        if not selected_types:
            QMessageBox.warning(self, "警告", "请至少选择一种抓取类型！")
            return
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.start_scraping.emit(selected_types)
    
    def on_stop_clicked(self):
        """停止按钮点击事件"""
        self.stop_scraping.emit()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
    
    def reset_buttons(self):
        """重置按钮状态"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)


class ProgressPanel(QWidget):
    """进度显示面板"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("执行状态")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # 状态显示
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 8px 12px;
                background-color: #f9fafb;
                border-radius: 6px;
                border: 1px solid #e5e7eb;
            }
        """)
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #10b981;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # 统计信息
        self.create_stats_group(layout)
        
        # 添加弹性空间
        layout.addStretch()
    
    def create_stats_group(self, parent_layout):
        """创建统计信息组"""
        group = QGroupBox("统计信息")
        group.setStyleSheet(get_group_style())
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # 批量处理进度
        layout.addWidget(QLabel("批量进度:"), 0, 0)
        self.batch_progress_label = QLabel("0/0")
        self.batch_progress_label.setStyleSheet("font-weight: bold; color: #2563eb;")
        layout.addWidget(self.batch_progress_label, 0, 1)
        
        # 当前处理ID
        layout.addWidget(QLabel("当前ID:"), 1, 0)
        self.current_id_label = QLabel("-")
        self.current_id_label.setStyleSheet("font-weight: bold; color: #7c3aed;")
        layout.addWidget(self.current_id_label, 1, 1)
        
        # 统计标签
        layout.addWidget(QLabel("已抓取用户:"), 2, 0)
        self.user_count_label = QLabel("0")
        self.user_count_label.setStyleSheet("font-weight: bold; color: #059669;")
        layout.addWidget(self.user_count_label, 2, 1)
        
        layout.addWidget(QLabel("已抓取粉丝:"), 3, 0)
        self.follower_count_label = QLabel("0")
        self.follower_count_label.setStyleSheet("font-weight: bold; color: #059669;")
        layout.addWidget(self.follower_count_label, 3, 1)
        
        layout.addWidget(QLabel("已抓取视频:"), 4, 0)
        self.video_count_label = QLabel("0")
        self.video_count_label.setStyleSheet("font-weight: bold; color: #059669;")
        layout.addWidget(self.video_count_label, 4, 1)
        
        layout.addWidget(QLabel("开始时间:"), 5, 0)
        self.start_time_label = QLabel("-")
        layout.addWidget(self.start_time_label, 5, 1)
        
        parent_layout.addWidget(group)
    
    def update_batch_progress(self, current: int, total: int):
        """更新批量进度"""
        self.batch_progress_label.setText(f"{current}/{total}")
    
    def update_current_id(self, douyin_id: str):
        """更新当前处理ID"""
        # 截断ID显示，避免太长
        display_id = douyin_id if len(douyin_id) <= 12 else douyin_id[:12] + "..."
        self.current_id_label.setText(display_id)
    
    def update_progress(self, value: int):
        """更新进度"""
        self.progress_bar.setValue(value)
    
    def update_status(self, status: str):
        """更新状态"""
        self.status_label.setText(status)
    
    def update_stats(self, stats: Dict):
        """更新统计信息"""
        if 'user_count' in stats:
            self.user_count_label.setText(str(stats['user_count']))
        if 'follower_count' in stats:
            self.follower_count_label.setText(str(stats['follower_count']))
        if 'video_count' in stats:
            self.video_count_label.setText(str(stats['video_count']))
        if 'start_time' in stats:
            self.start_time_label.setText(stats['start_time'])
    
    def reset_stats(self):
        """重置统计信息"""
        self.batch_progress_label.setText("0/0")
        self.current_id_label.setText("-")
        self.user_count_label.setText("0")
        self.follower_count_label.setText("0")
        self.video_count_label.setText("0")
        self.start_time_label.setText("-")
        self.progress_bar.setValue(0)


class LogPanel(QWidget):
    """日志显示面板"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题和控制按钮
        header_layout = QHBoxLayout()
        
        title = QLabel("运行日志")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #1f2937;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # 清除日志按钮
        clear_btn = QPushButton("清除日志")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        clear_btn.clicked.connect(self.clear_logs)
        header_layout.addWidget(clear_btn)
        
        layout.addLayout(header_layout)
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        
        # 限制最大行数 - 通过QTextDocument设置
        self.log_text.document().setMaximumBlockCount(1000)
        self.log_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background-color: #1f2937;
                color: #f9fafb;
                border: 1px solid #374151;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.log_text)
    
    def add_log(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 根据级别设置颜色
        color = "#f9fafb"  # 默认白色
        if level == "ERROR":
            color = "#ef4444"  # 红色
        elif level == "WARNING":
            color = "#f59e0b"  # 黄色
        elif level == "SUCCESS":
            color = "#10b981"  # 绿色
        
        formatted_message = f'<span style="color: #9ca3af;">[{timestamp}]</span> <span style="color: {color};">[{level}]</span> {message}'
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_logs(self):
        """清除所有日志"""
        self.log_text.clear()


class ResultPanel(QWidget):
    """结果展示面板"""
    
    def __init__(self):
        super().__init__()
        self.current_data = {}  # 存储当前显示的数据
        self.all_users_data = []  # 存储所有用户的数据（用于批量导出）
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题和导出按钮
        header_layout = QHBoxLayout()
        
        title = QLabel("结果预览")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #1f2937;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()

        # 清空列表按钮
        self.clear_btn = ModernButton("清空列表", "secondary")
        self.clear_btn.clicked.connect(self.clear_user_list)
        header_layout.addWidget(self.clear_btn)
        
        layout.addLayout(header_layout)
        
        # 结果选项卡
        self.result_tabs = QTabWidget()
        self.result_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f3f4f6;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2563eb;
            }
        """)
        
        # 初始化选项卡
        self.user_table = self.create_result_table()
        self.follower_table = self.create_result_table()
        self.favorite_table = self.create_result_table()

        self.result_tabs.addTab(self.user_table, "用户信息")
        # 隐藏粉丝列表和喜欢列表标签页
        # self.result_tabs.addTab(self.follower_table, "粉丝列表")
        # self.result_tabs.addTab(self.favorite_table, "喜欢列表")
        
        layout.addWidget(self.result_tabs)
    
    def create_result_table(self) -> QTableWidget:
        """创建结果表格"""
        table = QTableWidget()
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        table.horizontalHeader().setStretchLastSection(True)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e5e7eb;
                background-color: white;
                selection-background-color: #ddd6fe;
            }
            QHeaderView::section {
                background-color: #f3f4f6;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)
        return table
    
    def update_data(self, data_info: Dict):
        """更新结果数据"""
        data_type = data_info['type']
        data = data_info['data']

        self.current_data[data_type] = data

        if data_type == "user_profile":
            self.populate_user_table(data)
        # 隐藏粉丝列表和喜欢列表的数据展示
        # elif data_type == "followers":
        #     self.populate_follower_table(data)
        # elif data_type == "favorites":
        #     self.populate_favorite_table(data)
    
    def populate_user_table(self, user_data: Dict):
        """填充用户信息表格 - 累积添加用户"""
        if not user_data:
            return

        # 检查是否已存在该用户（根据uid去重）
        user_uid = user_data.get('uid', '')
        existing_user = False
        for existing_data in self.all_users_data:
            if existing_data.get('uid') == user_uid:
                existing_user = True
                break

        # 如果是新用户，添加到列表
        if not existing_user:
            self.all_users_data.append(user_data)

        # 重新填充整个表格
        self.user_table.setRowCount(len(self.all_users_data))
        headers = ["昵称", "用户ID", "抖音号", "粉丝数", "关注数", "作品数", "个人简介"]
        self.user_table.setColumnCount(len(headers))
        self.user_table.setHorizontalHeaderLabels(headers)

        # 填充所有用户数据
        for row, user in enumerate(self.all_users_data):
            row_data = [
                user.get('nickname', ''),
                user.get('uid', ''),
                user.get('unique_id', ''),
                str(user.get('followers', 0)),  # 使用正确的字段名
                str(user.get('following', 0)),  # 使用正确的字段名
                str(user.get('aweme_count', 0)),
                user.get('signature', '')
            ]

            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                self.user_table.setItem(row, col, item)

    def clear_user_list(self):
        """清空用户列表"""
        self.all_users_data.clear()
        self.user_table.setRowCount(0)
        self.current_data.clear()
    
    def populate_follower_table(self, follower_data: List[Dict]):
        """填充粉丝表格"""
        if not follower_data:
            return
        
        self.follower_table.setRowCount(len(follower_data))
        headers = ["昵称", "抖音号", "粉丝数", "关注数", "个人简介"]
        self.follower_table.setColumnCount(len(headers))
        self.follower_table.setHorizontalHeaderLabels(headers)
        
        for row, follower in enumerate(follower_data):
            row_data = [
                follower.get('用户昵称', ''),
                follower.get('用户抖音号', ''),
                str(follower.get('粉丝数', 0)),
                str(follower.get('关注数', 0)),
                follower.get('用户签名', '')
            ]
            
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                self.follower_table.setItem(row, col, item)
    
    def populate_favorite_table(self, favorite_data: List[Dict]):
        """填充喜欢列表表格"""
        if not favorite_data:
            return
        
        self.favorite_table.setRowCount(len(favorite_data))
        headers = ["视频ID", "描述", "作者", "点赞数", "评论数", "分享数", "创建时间"]
        self.favorite_table.setColumnCount(len(headers))
        self.favorite_table.setHorizontalHeaderLabels(headers)
        
        for row, video in enumerate(favorite_data):
            row_data = [
                video.get('视频id', ''),
                video.get('视频描述', ''),
                video.get('作者昵称', ''),
                str(video.get('视频点赞数', 0)),
                str(video.get('视频评论数', 0)),
                str(video.get('视频分享数', 0)),
                video.get('发布时间', '')
            ]
            
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                self.favorite_table.setItem(row, col, item)
    
