import time
import threading
import queue
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor, as_completed


class ConcurrentVideoFetcher:
    """多标签页并发视频信息获取器"""
    
    def __init__(self, scraper_instance, max_tabs: int):
        self.scraper = scraper_instance
        self.max_tabs = max_tabs
        self.tabs = []  # 标签页池
        self.tab_lock = threading.Lock()
        self.available_tabs = queue.Queue()  # 可用标签页队列
        self.logger = scraper_instance.logger
        self.results = []  # 线程安全的结果列表
        self.results_lock = threading.Lock()
        self.processed_ids = set()  # 去重集合
        self.error_count = 0
        self.success_count = 0
        
    def initialize_tabs(self):
        """初始化标签页池"""
        self.logger.info(f"开始初始化 {self.max_tabs} 个标签页")
        
        # 获取浏览器实例
        browser = self.scraper.dp.browser
        
        for i in range(self.max_tabs):
            try:
                # 创建新标签页，避免同时创建触发风控
                if i > 0:
                    time.sleep(self.scraper.TAB_INIT_DELAY)
                
                # 使用浏览器实例创建新标签页
                tab_id = browser.new_tab()
                tab = browser.get_tab(tab_id)
                
                # 为每个标签页设置监听器
                tab.listen.clear()
                tab.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")
                
                self.tabs.append(tab)
                self.available_tabs.put(tab)
                
                if self.scraper.CONCURRENT_DEBUG_LOGGING:
                    self.logger.debug(f"标签页 {i+1} 初始化完成，ID: {tab_id}")
                
            except Exception as e:
                self.logger.error(f"初始化标签页 {i+1} 失败: {e}")
                
        self.logger.info(f"标签页池初始化完成，可用标签页数量: {len(self.tabs)}")
        
    def get_tab(self) -> object:
        """获取可用标签页"""
        return self.available_tabs.get()
        
    def return_tab(self, tab):
        """归还标签页到池中"""
        self.available_tabs.put(tab)
        
    def cleanup(self):
        """清理标签页池并关闭相关标签页"""
        self.logger.info("开始清理标签页池")
        closed_count = 0
        failed_count = 0
        
        for i, tab in enumerate(self.tabs):
            try:
                # 停止监听器
                tab.listen.stop()
                
                # 关闭标签页
                tab.close()
                closed_count += 1
                
                if self.scraper.CONCURRENT_DEBUG_LOGGING:
                    self.logger.debug(f"成功关闭标签页 {i+1}")
                    
            except Exception as e:
                failed_count += 1
                self.logger.warning(f"清理标签页 {i+1} 时出现异常: {e}")
        
        # 清空标签页列表和队列
        self.tabs.clear()
        
        # 清空可用标签页队列
        while not self.available_tabs.empty():
            try:
                self.available_tabs.get_nowait()
            except queue.Empty:
                break
        
        self.logger.info(f"标签页池清理完成 - 成功关闭: {closed_count}, 失败: {failed_count}")
                
    def add_result(self, result: Dict, video_id: str):
        """线程安全地添加结果"""
        with self.results_lock:
            if video_id not in self.processed_ids:
                self.results.append(result)
                self.processed_ids.add(video_id)
                self.success_count += 1
                
    def add_error(self):
        """记录错误数量"""
        with self.results_lock:
            self.error_count += 1


def fetch_video_info_in_tab(scraper, tab, video_id: str) -> Dict:
    """在指定标签页中获取视频信息 - 并发专用方法"""
    if scraper.CONCURRENT_DEBUG_LOGGING:
        scraper.logger.debug(f"在标签页中开始获取视频详情 - ID: {video_id}")
    
    video_url = f"https://www.douyin.com/video/{video_id}"
    
    try:
        # 清理监听器队列中的旧数据包
        if scraper.CONCURRENT_DEBUG_LOGGING:
            scraper.logger.debug(f"清理标签页监听器队列 - ID: {video_id}")
        
        cleaned_packets = 0
        for cleanup_round in range(scraper.LISTENER_CLEANUP_ROUNDS):
            round_cleaned = 0
            try:
                while True:
                    try:
                        old_pkt = tab.listen.wait(timeout=scraper.LISTENER_CLEANUP_TIMEOUT, raise_err=True)
                        round_cleaned += 1
                        cleaned_packets += 1
                        if round_cleaned > 5:  # 防止无限循环
                            break
                    except:
                        break
            except Exception:
                break
                
            if round_cleaned == 0:
                break
                
        if scraper.CONCURRENT_DEBUG_LOGGING and cleaned_packets > 0:
            scraper.logger.debug(f"清理了 {cleaned_packets} 个旧数据包 - ID: {video_id}")
        
        # 访问视频页面
        tab.get(video_url)
        
        # 获取响应数据
        data = None
        last_error = ""
        
        for retry_count in range(scraper.JS_RETRY):
            try:
                pkt = tab.listen.wait(timeout=scraper.JS_TIMEOUT, raise_err=True)
                data = scraper._to_json(pkt.response.body)
                
                if data and data.get('aweme_detail'):
                    # 验证视频ID一致性
                    if scraper.STRICT_DATA_VALIDATION:
                        received_video_id = data.get('aweme_detail', {}).get('aweme_id', '')
                        if received_video_id == video_id:
                            if scraper.CONCURRENT_DEBUG_LOGGING:
                                scraper.logger.debug(f"并发模式验证通过 - ID: {video_id}")
                            break
                        else:
                            if scraper.CONCURRENT_DEBUG_LOGGING:
                                scraper.logger.warning(f"并发模式ID不匹配 - 请求: {video_id}, 接收: {received_video_id}")
                            data = None
                            continue
                    else:
                        break
                else:
                    if scraper.CONCURRENT_DEBUG_LOGGING:
                        scraper.logger.warning(f"响应中无aweme_detail - ID: {video_id}, 重试: {retry_count + 1}")
                    
            except Exception as e:
                last_error = str(e)
                if scraper.CONCURRENT_DEBUG_LOGGING:
                    scraper.logger.warning(f"获取响应异常 - ID: {video_id}, 错误: {e}, 重试: {retry_count + 1}")
                
            if retry_count < scraper.JS_RETRY - 1:
                time.sleep(scraper.SLEEP_BETWEEN_TRIES)
        
        # 处理获取到的数据
        video_detail = (data or {}).get('aweme_detail')
        if video_detail:
            return scraper._process_video_detail(video_detail, video_id)
        else:
            # 对于并发模式，技术错误时返回基本信息而不是触发滑块验证
            error_type = scraper._classify_error(last_error, data)
            scraper.logger.warning(f"并发模式检测到技术错误({error_type})，返回基本信息 - ID: {video_id}")
            if scraper.CONCURRENT_DEBUG_LOGGING:
                scraper.logger.debug(f"最后错误: {last_error}")
                scraper.logger.debug(f"响应数据: {str(data)[:200]}...")
            return get_basic_video_info(video_id)
            
    except Exception as e:
        scraper.logger.error(f"并发获取视频详情异常 - ID: {video_id}, 错误: {e}")
        return get_basic_video_info(video_id)


def get_basic_video_info(video_id: str) -> Dict:
    """获取基本视频信息（当详细信息获取失败时使用）"""
    # 使用统一的基本信息函数，确保字段一致性
    from douyin_data import get_basic_video_info as data_get_basic_video_info
    return data_get_basic_video_info(video_id)


def concurrent_worker(scraper, fetcher: ConcurrentVideoFetcher, video_ids: List[str]):
    """并发工作线程函数"""
    thread_name = threading.current_thread().name
    
    for video_id in video_ids:
        try:
            # 获取标签页
            tab = fetcher.get_tab()
            
            if scraper.CONCURRENT_DEBUG_LOGGING:
                scraper.logger.debug(f"[{thread_name}] 开始处理视频 - ID: {video_id}")
            
            start_time = time.time()
            
            # 获取视频信息
            video_info = fetch_video_info_in_tab(scraper, tab, video_id)

            # 保存结果
            fetcher.add_result(video_info, video_id)

            elapsed_time = time.time() - start_time

            # 检查是否获取到了完整信息
            if video_info.get('视频描述') or video_info.get('作者昵称'):
                scraper.logger.info(f"[{thread_name}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")
            else:
                scraper.logger.warning(f"[{thread_name}] 获取到基本信息（详情获取失败） - ID: {video_id}, 耗时: {elapsed_time:.2f}s")
            
            # 归还标签页
            fetcher.return_tab(tab)
            
            # 防风控延迟
            time.sleep(scraper.SLEEP_BETWEEN_PAGES)
            
        except Exception as e:
            fetcher.add_error()
            scraper.logger.error(f"[{thread_name}] 处理视频失败 - ID: {video_id}, 错误: {e}")
            
            # 确保标签页被归还
            try:
                fetcher.return_tab(tab)
            except:
                pass


def fetch_favorites_concurrent(scraper, sec_uid: str, max_items: int = 200) -> List[Dict]:
    """
    并发版本的喜欢视频获取方法 - 使用多标签页并发处理
    
    Args:
        scraper: DouyinScraper实例
        sec_uid (str): 用户的sec_uid
        max_items (int): 最大获取数量
        
    Returns:
        List[Dict]: 视频详情列表
    """
    
    if not scraper.ENABLE_CONCURRENT_MODE:
        scraper.logger.warning("并发模式未启用，回退到串行模式")
        # 这里需要导入数据模块的方法
        from douyin_data import fetch_favorites_optimized
        return fetch_favorites_optimized(scraper, sec_uid, max_items)
        
    scraper.logger.info(f"启用并发模式获取喜欢列表 - 并发数: {scraper.MAX_CONCURRENT_TABS}")
    
    # 获取视频列表（需要从数据模块导入）
    from douyin_data import get_video_list
    video_items = get_video_list(scraper, sec_uid, max_items)
    
    if not video_items:
        scraper.logger.warning("未获取到视频列表")
        return []
    
    # 提取视频ID列表
    video_ids = []
    for item in video_items:
        video_id = item.get('aweme_id')
        if video_id:
            video_ids.append(video_id)
    
    if not video_ids:
        scraper.logger.warning("未提取到有效的视频ID")
        return []
        
    scraper.logger.info(f"准备并发处理 {len(video_ids)} 个视频")
    
    # 创建并发获取器
    fetcher = ConcurrentVideoFetcher(scraper, scraper.MAX_CONCURRENT_TABS)
    
    try:
        # 初始化标签页池
        fetcher.initialize_tabs()
        
        if len(fetcher.tabs) == 0:
            scraper.logger.error("标签页池初始化失败，回退到串行模式")
            from douyin_data import fetch_favorites_optimized
            return fetch_favorites_optimized(scraper, sec_uid, max_items)
        
        # 分批处理视频ID
        batch_size = max(1, len(video_ids) // len(fetcher.tabs))
        video_batches = [video_ids[i:i + batch_size] for i in range(0, len(video_ids), batch_size)]
        
        scraper.logger.info(f"将 {len(video_ids)} 个视频分为 {len(video_batches)} 批，每批约 {batch_size} 个")
        
        # 使用线程池执行并发任务
        with ThreadPoolExecutor(max_workers=len(fetcher.tabs), thread_name_prefix="ConcurrentVideo") as executor:
            futures = []
            
            for batch in video_batches:
                if batch:  # 确保批次不为空
                    future = executor.submit(concurrent_worker, scraper, fetcher, batch)
                    futures.append(future)
            
            # 等待所有任务完成
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    scraper.logger.error(f"并发任务执行异常: {e}")
        
        # 收集结果并统计详细信息
        detailed_count = 0
        basic_count = 0

        for result in fetcher.results:
            if result.get('视频描述') or result.get('作者昵称'):
                detailed_count += 1
            else:
                basic_count += 1

        scraper.logger.info(f"并发处理完成 - 详细信息: {detailed_count}, 基本信息: {basic_count}, 失败: {fetcher.error_count}, 总计: {len(video_ids)}")

        if basic_count > 0:
            scraper.logger.warning(f"有 {basic_count} 个视频只获取到基本信息，可能需要检查网络或调整并发参数")

        return fetcher.results[:max_items]
        
    except Exception as e:
        scraper.logger.error(f"并发处理异常: {e}")
        return []
        
    finally:
        # 清理标签页池
        fetcher.cleanup()