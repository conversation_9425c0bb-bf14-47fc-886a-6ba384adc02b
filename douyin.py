# 导入核心类
from douyin_core import DouyinScraper as CoreScraper

# 导入数据处理模块的函数
from douyin_data import (
    fetch_favorites,
    fetch_favorites_optimized,
    fetch_favorites_stage1_optimized,
    fetch_favorites_stage2_optimized,
    fetch_favorites_stage3_optimized,
    get_video_list,
    fetch_video_info_optimized,
    get_basic_video_info,
    process_video_detail
)

# 导入并发处理模块的函数
from douyin_concurrent import (
    ConcurrentVideoFetcher,
    fetch_favorites_concurrent,
    fetch_video_info_in_tab,
    concurrent_worker
)


class DouyinScraper(CoreScraper):
    """
    抖音网页数据抓取器扩展类，继承核心功能并添加数据处理和并发功能。
    """
    
    def fetch_favorites(self, sec_uid: str, max_items: int = 200, privacy_check: dict = None):
        """获取用户点赞的视频列表，根据配置自动选择串行或并发模式"""
        # 首先检查是否启用并发模式（优先级最高）
        if self.ENABLE_CONCURRENT_MODE and self.MAX_CONCURRENT_TABS > 1:
            self.logger.info(f"检测到并发模式启用 - 并发标签页数: {self.MAX_CONCURRENT_TABS}")
            # 并发模式暂时不支持privacy_check参数，先进行隐私检查
            if privacy_check and not privacy_check.get('favorites_visible', True):
                self.logger.info(f"喜欢列表不可见: {privacy_check.get('favorites_reason', '未知原因')}")
                return []
            from douyin_concurrent import fetch_favorites_concurrent
            return fetch_favorites_concurrent(self, sec_uid, max_items)

        # 如果未启用并发模式，且传入了隐私检查参数，使用支持该参数的优化版本
        elif privacy_check is not None:
            # 根据优化阶段选择对应的方法
            if hasattr(self, 'OPTIMIZATION_STAGE') and self.OPTIMIZATION_STAGE == 2:
                return fetch_favorites_stage2_optimized(self, sec_uid, max_items, privacy_check)
            elif hasattr(self, 'OPTIMIZATION_STAGE') and self.OPTIMIZATION_STAGE == 3:
                return fetch_favorites_stage3_optimized(self, sec_uid, max_items, privacy_check)
            else:
                # 默认使用阶段1优化
                return fetch_favorites_stage1_optimized(self, sec_uid, max_items, privacy_check)
        else:
            # 没有privacy_check参数，使用原始的fetch_favorites（会再次检查并发模式）
            return fetch_favorites(self, sec_uid, max_items)
    
    def fetch_favorites_optimized(self, sec_uid: str, max_items: int = 200):
        """统一的优化方法，根据配置文件自动选择优化阶段"""
        return fetch_favorites_optimized(self, sec_uid, max_items)
    
    def fetch_favorites_stage1_optimized(self, sec_uid: str, max_items: int = 200):
        """阶段一：监听器复用优化"""
        return fetch_favorites_stage1_optimized(self, sec_uid, max_items)
    
    def fetch_favorites_stage2_optimized(self, sec_uid: str, max_items: int = 200):
        """阶段二：监听器复用 + 队列清理机制优化"""
        return fetch_favorites_stage2_optimized(self, sec_uid, max_items)
    
    def fetch_favorites_stage3_optimized(self, sec_uid: str, max_items: int = 200):
        """阶段三：监听器复用 + 队列清理 + 加载模式优化"""
        return fetch_favorites_stage3_optimized(self, sec_uid, max_items)
    
    def fetch_favorites_concurrent(self, sec_uid: str, max_items: int = 200):
        """并发版本的喜欢视频获取方法"""
        return fetch_favorites_concurrent(self, sec_uid, max_items)
    
    def _get_video_list(self, sec_uid: str, max_items: int):
        """获取视频列表的辅助方法"""
        return get_video_list(self, sec_uid, max_items)
    
    def _fetch_video_info_optimized(self, video_id: str):
        """优化版本的视频信息获取"""
        return fetch_video_info_optimized(self, video_id)
    
    def _get_basic_video_info(self, video_id: str):
        """获取基本视频信息"""
        return get_basic_video_info(video_id)
    
    def _process_video_detail(self, video_detail: dict, video_id: str):
        """处理视频详情数据"""
        return process_video_detail(video_detail, video_id)
    
    def _fetch_video_info_in_tab(self, tab, video_id: str):
        """在指定标签页中获取视频信息"""
        return fetch_video_info_in_tab(self, tab, video_id)
    
    def _concurrent_worker(self, fetcher, video_ids):
        """并发工作线程函数"""
        return concurrent_worker(self, fetcher, video_ids)
    
    # 保持ConcurrentVideoFetcher作为内部类以维持兼容性
    ConcurrentVideoFetcher = ConcurrentVideoFetcher