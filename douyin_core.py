from DrissionPage import Chromium, ChromiumOptions
import jmespath
import json
from urllib.parse import urlsplit, parse_qsl, urlencode, urlunsplit
import time
import logging
import re
import random
from typing import List, Dict, Set, Union
from logger import setup_logger
from utils import load_config
import datetime


class ListenerManager:
    """监听器管理器 - 优化监听器使用模式，避免频繁重置"""
    
    def __init__(self, dp_instance, logger):
        self.dp = dp_instance
        self.logger = logger
        self.current_endpoint = None
        self.is_active = False
    
    def setup_listener(self, endpoint: str, force_reset: bool = False):
        """
        智能设置监听器，避免不必要的重置操作
        
        Args:
            endpoint (str): 要监听的API端点
            force_reset (bool): 是否强制重置监听器
        """
        if self.current_endpoint == endpoint and self.is_active and not force_reset:
            self.logger.debug(f"监听器已设置相同端点，跳过重置: {endpoint}")
            return
        
        self.logger.debug(f"设置监听器: {endpoint}")
        self.dp.listen.clear()
        self.dp.listen.start(endpoint)
        self.current_endpoint = endpoint
        self.is_active = True
    
    def pause_listener(self):
        """暂停监听器"""
        if self.is_active:
            self.dp.listen.pause(True)
            self.is_active = False
            self.logger.debug("监听器已暂停")
    
    def stop_listener(self):
        """停止监听器"""
        if self.is_active:
            self.dp.listen.stop()
            self.is_active = False
            self.current_endpoint = None
            self.logger.debug("监听器已停止")


class DouyinScraper:
    """抖音网页数据抓取器，基于 DrissionPage。"""

    # ---- 全局运行参数（按需修改） ----
    JS_TIMEOUT = 10                # 每次 run_js 的超时时间（秒）- 从3秒增加到10秒，减少误判超时
    JS_RETRY = 3                   # 超时或返回空时的自动重试次数（每个阶段）- 从2次增加到3次，提高成功率
    SLEEP_BETWEEN_TRIES = 0.8      # 单页内部重试的间隔（秒）
    SLEEP_BETWEEN_PAGES = 1.5      # 翻页间隔，降低风控概率
    VIDEO_RETRY_BASE_DELAY = 0.8   # 视频信息获取首次重试延迟（秒）
    VIDEO_RETRY_EXTRA_DELAY_MIN = 0.5  # 视频信息获取后续重试额外延迟最小值（秒）
    VIDEO_RETRY_EXTRA_DELAY_MAX = 0.8  # 视频信息获取后续重试额外延迟最大值（秒）

    def __init__(self, ui_callback=None):
        """初始化 Chromium 实例并配置个人数据目录.

        Args:
            ui_callback: UI模式下的滑块验证回调函数
        """
        options = ChromiumOptions()
        # options.no_imgs(True)
        options.set_user_data_path(r'D:\temp\dp_profile_clean')
        options.set_load_mode('none').mute(True)
        options.set_argument('--disable-gpu')   # 禁用GPU加速
        options.set_argument('--disable-dev-shm-usage')  # 减少内存使用
        options.set_argument('--disable-extensions')     # 禁用扩展
        options.set_argument('--disable-plugins')        # 禁用插件
        # options.set_user_data_path(user_data_path)
        # self.dp = Chromium(options).latest_tab
        self.dp = Chromium(options).latest_tab
        self.logger = setup_logger("DouyinScraper")
        
        # UI回调函数，用于在UI模式下处理滑块验证
        self.ui_callback = ui_callback
        
        # 初始化监听器管理器
        self.listener_manager = ListenerManager(self.dp, self.logger)

        # 加载配置并更新类变量
        self.config = load_config()
        self._update_config()

    def _update_config(self):
        """
        从配置文件更新类变量，保持向后兼容。
        """
        scraper_config = self.config.get('scraper', {})
        self.JS_TIMEOUT = scraper_config.get('js_timeout', self.JS_TIMEOUT)
        self.JS_RETRY = scraper_config.get('js_retry', self.JS_RETRY)
        self.SLEEP_BETWEEN_TRIES = scraper_config.get('sleep_between_tries', self.SLEEP_BETWEEN_TRIES)
        self.SLEEP_BETWEEN_PAGES = scraper_config.get('sleep_between_pages', self.SLEEP_BETWEEN_PAGES)
        self.VIDEO_RETRY_BASE_DELAY = scraper_config.get('video_retry_base_delay', self.VIDEO_RETRY_BASE_DELAY)
        self.VIDEO_RETRY_EXTRA_DELAY_MIN = scraper_config.get('video_retry_extra_delay_min', self.VIDEO_RETRY_EXTRA_DELAY_MIN)
        self.VIDEO_RETRY_EXTRA_DELAY_MAX = scraper_config.get('video_retry_extra_delay_max', self.VIDEO_RETRY_EXTRA_DELAY_MAX)

        # 数据质量配置
        data_quality_config = self.config.get('data_quality', {})
        self.LISTENER_CLEANUP_TIMEOUT = data_quality_config.get('listener_cleanup_timeout', 1.0)
        self.LISTENER_CLEANUP_ROUNDS = data_quality_config.get('listener_cleanup_rounds', 3)
        self.STRICT_DATA_VALIDATION = data_quality_config.get('strict_data_validation', True)
        self.ENABLE_DEDUPLICATION = data_quality_config.get('enable_deduplication', True)

        # 优化配置
        optimization_config = self.config.get('optimization', {})
        self.OPTIMIZATION_STAGE = optimization_config.get('optimization_stage', 3)
        self.ENABLE_PERFORMANCE_STATS = optimization_config.get('enable_performance_stats', True)
        self.ENABLE_DEBUG_LOGGING = optimization_config.get('enable_debug_logging', False)
        self.BATCH_SIZE = optimization_config.get('batch_size', 10)

        # 并发配置
        concurrent_config = self.config.get('concurrent', {})
        self.ENABLE_CONCURRENT_MODE = concurrent_config.get('enable_concurrent_mode', True)
        self.MAX_CONCURRENT_TABS = concurrent_config.get('max_concurrent_tabs', 3)
        self.TAB_INIT_DELAY = concurrent_config.get('tab_init_delay', 2.0)
        self.CONCURRENT_BATCH_SIZE = concurrent_config.get('concurrent_batch_size', 10)
        self.CONCURRENT_RETRY_COUNT = concurrent_config.get('concurrent_retry_count', 2)
        self.CONCURRENT_DEBUG_LOGGING = concurrent_config.get('concurrent_debug_logging', False)

    def _setup_listener_and_get(self, endpoint: str, url: str, expected_field: str = None):
        """
        统一的监听器设置和数据获取逻辑，使用优化的监听器管理器。

        Args:
            endpoint (str): 要监听的API端点
            url (str): 要访问的页面URL
            expected_field (str, optional): 期望在响应中存在的字段名

        Returns:
            tuple: (request_url, response_data) 或 (None, None) 如果失败
        """
        # 使用优化的监听器管理器，避免不必要的重置
        self.listener_manager.setup_listener(endpoint)
        self.dp.get(url)

        for retry_count in range(self.JS_RETRY):
            try:
                pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                data = self._to_json(pkt.response.body)

                # 如果指定了期望字段，检查是否存在
                if expected_field and not data.get(expected_field):
                    self.logger.warning(f"响应中无 {expected_field} 字段, 重试: {retry_count + 1}")
                    if retry_count < self.JS_RETRY - 1:
                        time.sleep(self.SLEEP_BETWEEN_TRIES)
                    continue

                return pkt.request.url, data

            except Exception as e:
                self.logger.warning(f"获取响应异常: {e}, 重试: {retry_count + 1}")
                if retry_count < self.JS_RETRY - 1:
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

        return None, None

    def _get_video_retry_delay(self, retry_count: int) -> float:
        """
        计算视频信息获取的重试延迟时间。

        Args:
            retry_count (int): 当前重试次数（从0开始）

        Returns:
            float: 延迟时间（秒）
        """
        if retry_count == 0:
            # 第一次重试使用基础延迟
            return self.VIDEO_RETRY_BASE_DELAY
        else:
            # 后续重试在基础延迟基础上添加随机延迟
            extra_delay = random.uniform(self.VIDEO_RETRY_EXTRA_DELAY_MIN, self.VIDEO_RETRY_EXTRA_DELAY_MAX)
            total_delay = self.VIDEO_RETRY_BASE_DELAY + extra_delay
            return total_delay

    # ---------------- 工具方法 ----------------

    def _to_json(self, body) -> dict:
        """将 response.body 统一转换为 dict，增加异常处理避免JSON解析错误导致程序崩溃."""
        if not body:
            return {}

        try:
            if isinstance(body, (bytes, bytearray)):
                body_str = body.decode('utf-8', 'ignore')
            elif isinstance(body, str):
                body_str = body
            else:
                return body or {}

            # 清理可能的分块传输编码标识符
            # 查找第一个 '{' 和最后一个 '}' 字符，提取完整的JSON
            json_start = body_str.find('{')
            json_end = body_str.rfind('}')

            if json_start >= 0 and json_end > json_start:
                body_str = body_str[json_start:json_end + 1]
                self.logger.debug(f"清理了前后缀字符，提取JSON: 位置 {json_start} 到 {json_end}")
            elif json_start >= 0:
                body_str = body_str[json_start:]
                self.logger.debug(f"清理了前缀字符，从位置 {json_start} 开始解析JSON")

            return json.loads(body_str)
        except json.JSONDecodeError as e:
            # JSON解析错误不应该导致程序崩溃，记录警告并返回空字典
            self.logger.warning(f"JSON解析失败: {e}")
            # 添加调试信息：显示原始响应数据
            if isinstance(body, str):
                self.logger.warning(f"原始响应数据: {body[:500]}...")  # 只显示前500字符
                self.logger.warning(f"数据长度: {len(body)}")
                self.logger.warning(f"最后100字符: ...{body[-100:]}")
            elif isinstance(body, (bytes, bytearray)):
                body_str = body.decode('utf-8', 'ignore')
                self.logger.warning(f"原始响应数据: {body_str[:500]}...")
                self.logger.warning(f"数据长度: {len(body_str)}")
                self.logger.warning(f"最后100字符: ...{body_str[-100:]}")

            # 检查是否是"Extra data"错误（通常表示JSON后面有额外数据）
            if "Extra data" in str(e):
                try:
                    # 尝试只解析到第一个完整的JSON对象
                    decoder = json.JSONDecoder()
                    obj, idx = decoder.raw_decode(body_str)
                    self.logger.info(f"成功解析部分JSON，忽略位置{idx}后的额外数据")
                    return obj
                except Exception as partial_e:
                    self.logger.warning(f"部分JSON解析也失败: {partial_e}")
                    # 继续执行下面的清理逻辑

            # 尝试使用更激进的清理方法
            try:
                if isinstance(body, str):
                    clean_body = body
                else:
                    clean_body = body.decode('utf-8', 'ignore')

                # 移除所有非JSON字符，只保留大括号内的内容
                import re

                # 先清理分块传输编码的前缀和后缀
                lines = clean_body.split('\n')
                json_lines = []
                in_json = False

                for line in lines:
                    if line.strip().startswith('{'):
                        in_json = True
                    if in_json:
                        json_lines.append(line)
                    if line.strip().endswith('}') and in_json:
                        break

                if json_lines:
                    json_str = '\n'.join(json_lines)
                    self.logger.warning(f"尝试解析清理后的JSON，长度: {len(json_str)}")
                    self.logger.warning(f"JSON开头: {json_str[:100]}")
                    self.logger.warning(f"JSON结尾: {json_str[-100:]}")
                    return json.loads(json_str)

                # 备用方法：使用正则表达式
                json_match = re.search(r'\{.*\}', clean_body, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    self.logger.warning(f"正则提取的JSON，长度: {len(json_str)}")
                    return json.loads(json_str)
            except Exception as fallback_e:
                self.logger.warning(f"备用解析也失败: {fallback_e}")

            return {}
        except Exception as e:
            # 其他异常也进行处理
            self.logger.error(f"数据处理异常: {e}")
            return {}

    @staticmethod
    def _set_query_params(url: str, **kw) -> str:
        """在 URL 上修改或添加查询参数."""
        u = urlsplit(url)
        q = dict(parse_qsl(u.query, keep_blank_values=True))
        for k, v in kw.items():
            if v is not None:
                q[k] = str(v)
        return urlunsplit((u.scheme, u.netloc, u.path, urlencode(q, doseq=True), u.fragment))

    def _classify_error(self, error_msg: str, data: dict = None) -> str:
        """错误分类，避免将技术错误误判为滑块验证"""
        error_str = str(error_msg).lower()

        if "expecting value" in error_str or "json" in error_str:
            return "json_parse_error"    # JSON解析错误
        elif "timeout" in error_str:
            return "timeout_error"       # 超时错误
        elif "captcha" in error_str or "验证" in error_str:
            return "captcha_required"    # 滑块验证
        elif data is None or (isinstance(data, dict) and not data):
            return "empty_response"      # 空响应
        else:
            return "unknown_error"       # 未知错误

    def _should_trigger_captcha(self, data: dict, error_msg: str = "") -> bool:
        """判断是否真的需要滑块验证，避免将技术错误误判为人工验证需求"""
        error_type = self._classify_error(error_msg, data)

        # JSON解析错误、超时错误不是滑块问题
        if error_type in ["json_parse_error", "timeout_error"]:
            return False

        # 明确的验证需求
        if error_type == "captcha_required":
            return True

        # 空响应可能需要验证，但要更谨慎
        if error_type == "empty_response":
            return not data  # 只有在确实没有数据时才触发

        return False  # 其他情况不触发验证

    def _wait_user_to_solve(self, scene: str = '操作'):
        """在终端提示用户去浏览器完成滑块/安全验证，完成后回车继续。"""
        self.logger.warning(f"触发滑块/安全验证 - 场景: {scene}")
        
        # 如果有UI回调函数，使用UI模式
        if self.ui_callback:
            self.logger.info("使用UI模式处理滑块验证")
            success = self.ui_callback(scene)
            if not success:
                self.logger.info("用户取消验证，终止当前任务")
                raise RuntimeError("用户取消，终止当前任务。")
            self.logger.info(f"用户完成验证，继续执行 - 场景: {scene}")
            return
        
        # 命令行模式的原有逻辑
        print(f"\n[提示] 可能触发了滑块/安全验证（{scene}）。")
        print("        请切换到已打开的浏览器页面完成验证。")
        print("        完成后回到本窗口，按 Enter 继续；输入 q 然后回车可终止。\n")
        ack = input("完成验证后按 Enter 继续（或输入 q 退出）：").strip().lower()
        if ack == 'q':
            self.logger.info("用户选择退出任务")
            raise RuntimeError("用户取消，终止当前任务。")
        self.logger.info(f"用户完成验证，继续执行 - 场景: {scene}")

    def _fetch_json_via_js(self, url: str, scene: str) -> dict | None:
        """统一的 JS 拉取包装：任何 JS 超时/空返回都按触发滑块处理，并能在完成验证后继续原进度。

        流程：
            - 尝试 JS_RETRY 次，run_js 超时时间 JS_TIMEOUT；
            - 仍失败 → 提示用户过滑块；
            - 再尝试 JS_RETRY 次；
            - 若还失败，则抛错。
        """
        js = f"""
return (async () => {{
  const r = await fetch('{url}', {{ credentials: 'include' }});
  try {{ return await r.json(); }} catch (e) {{ return null; }}
}})()
"""

        # 阶段 1：自动重试
        for _ in range(self.JS_RETRY):
            try:
                data = self.dp.run_js(js, timeout=self.JS_TIMEOUT)
            except Exception:
                data = None
            if data:
                return data
            time.sleep(self.SLEEP_BETWEEN_TRIES)

        # 视为滑块
        self._wait_user_to_solve(scene)

        # 阶段 2：完成验证后再次重试
        for _ in range(self.JS_RETRY):
            try:
                data = self.dp.run_js(js, timeout=self.JS_TIMEOUT)
            except Exception:
                data = None
            if data:
                return data
            time.sleep(self.SLEEP_BETWEEN_TRIES)

        raise RuntimeError(f"多次尝试后仍未获取到数据（{scene}）。")

    # ---------------- 业务方法 ----------------

    def fetch_sec_uid(self, douyin_id: str) -> str:
        """通过搜索建议接口获取用户的 sec_uid。"""
        self.logger.info(f"开始获取用户 sec_uid - 抖音ID: {douyin_id}")

        endpoint = f"/aweme/v1/web/general/search"
        url = f'https://www.douyin.com/search/{douyin_id}'

        try:
            _, data = self._setup_listener_and_get(endpoint, url)
            # print(f'data: {data}')
            if not data:
                self.logger.error(f"获取搜索建议失败 - 抖音ID: {douyin_id}")
                raise RuntimeError("获取搜索建议失败")

            # 检查是否触发了验证检查
            search_nil_info = data.get('search_nil_info', {})
            if search_nil_info.get('search_nil_type') == 'verify_check':
                self.logger.warning(f"检测到需要验证检查 - 抖音ID: {douyin_id}")
                self._wait_user_to_solve('获取用户sec_uid（搜索验证）')

                # 验证完成后重新获取数据
                for retry_count in range(self.JS_RETRY):
                    self.dp.refresh()
                    try:
                        _, data = self._setup_listener_and_get(endpoint, url)
                        if data and data.get('data'):
                            break
                        else:
                            self.logger.warning(f"验证后仍无数据，重试: {retry_count + 1}")
                    except Exception as e:
                        self.logger.warning(f"验证后重试异常: {e}, 重试: {retry_count + 1}")
                        data = None

                    if retry_count < self.JS_RETRY - 1:
                        time.sleep(self.SLEEP_BETWEEN_TRIES)

                if not data or not data.get('data'):
                    raise RuntimeError("验证完成后仍无法获取搜索数据")

            #遍历数组
            #$.data
            res_list = jmespath.search('data', data)
            if not res_list:
                self.logger.error(f"搜索建议中无数据 - 抖音ID: {douyin_id}")
                raise RuntimeError("搜索建议中无数据")

            # print(f'res_list: {res_list}')
            # print(f'res_list type: {type(res_list)}')

            # 尝试多种数据结构提取sec_uid
            sec_uid = None

            # 方法1：从用户列表中提取（用户搜索结果）
            sec_uid = jmespath.search('[0].user_list[0].user_info.sec_uid', res_list)

            # 方法2：从视频信息中提取（视频搜索结果）
            if not sec_uid:
                sec_uid = jmespath.search('[0].aweme_info.author.sec_uid', res_list)
                if sec_uid:
                    self.logger.info(f"从视频作者信息中提取到 sec_uid - 抖音ID: {douyin_id}")

            # 方法3：从其他可能的结构中提取
            if not sec_uid:
                # 尝试直接从第一个结果的各种可能位置提取
                for item in res_list:
                    if item.get('type') == 4 and 'user_list' in item:  # 用户类型
                        user_info = jmespath.search('user_list[0].user_info', item)
                        if user_info and user_info.get('sec_uid'):
                            sec_uid = user_info['sec_uid']
                            self.logger.info(f"从用户类型结果中提取到 sec_uid - 抖音ID: {douyin_id}")
                            break
                    elif item.get('type') == 1 and 'aweme_info' in item:  # 视频类型
                        author_info = jmespath.search('aweme_info.author', item)
                        if author_info and author_info.get('sec_uid'):
                            sec_uid = author_info['sec_uid']
                            self.logger.info(f"从视频类型结果中提取到 sec_uid - 抖音ID: {douyin_id}")
                            break

            if not sec_uid:
                self.logger.error(f"未找到 sec_uid - 抖音ID: {douyin_id}")
                # 添加调试信息：显示实际的数据结构
                self.logger.debug(f"搜索结果结构: {json.dumps(res_list[:1], ensure_ascii=False, indent=2)}")
                raise RuntimeError("未在搜索响应中找到 sec_uid。")

            self.logger.info(f"成功获取 sec_uid: {sec_uid} - 抖音ID: {douyin_id}")
            return sec_uid

        finally:
            self.listener_manager.pause_listener()

    def fetch_user_profile(self, sec_uid: str) -> dict:
        """获取用户基本信息，包含隐私状态字段."""
        endpoint = "/aweme/v1/web/user/profile/other/?device_platform=webapp"
        url = f'https://www.douyin.com/user/{sec_uid}?from_tab_name=main'

        try:
            _, data = self._setup_listener_and_get(endpoint, url, 'user')

            if not data:
                self.logger.error(f"获取用户资料失败 - sec_uid: {sec_uid}")
                raise RuntimeError("获取用户资料失败")

            profile = data.get('user', {}) or {}
            
            # 基础信息
            basic_info = {
                'nickname': profile.get('nickname'),
                'uid': profile.get('uid'),
                'unique_id': profile.get('unique_id'),
                'followers': profile.get('mplatform_followers_count'),
                'following': profile.get('following_count'),
                'signature': profile.get('signature'),
                'aweme_count': profile.get('aweme_count'),
                'favoriting_count': profile.get('favoriting_count'),
            }
            
            # 隐私状态字段
            privacy_fields = {
                'secret': profile.get('secret'),
                'special_state_info': profile.get('special_state_info', {}),
                'show_favorite_list': profile.get('show_favorite_list'),
                'general_permission': profile.get('general_permission', {}),
            }
            
            # 合并并返回
            return {**basic_info, **privacy_fields}
            
        finally:
            self.listener_manager.pause_listener()

    def fetch_followers(self,
                        sec_uid: str,
                        max_items: int = 5000,
                        page_count: int = 20) -> List[Dict]:
        """抓取用户粉丝列表（PC Web），时间游标分页。"""
        # 使用优化的监听器管理器
        self.listener_manager.setup_listener("/aweme/v1/web/user/follower/list/")

        # 进入主页 -> 点击"粉丝"，触发首包
        self.dp.get(f"https://www.douyin.com/user/{sec_uid}?from_tab_name=main")
        locator = 'x://div[@data-e2e="user-info-fans"]'
        self.dp.wait.ele_displayed(locator, timeout=15)
        self.dp.ele(locator).click()

        try:
            # ---- 首包 ----
            first_req_url, first_data = None, None
            for retry_count in range(self.JS_RETRY):
                try:
                    # 使用推荐的 listen.wait() API
                    pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                    first_req_url = pkt.request.url
                    first_data = self._to_json(pkt.response.body)
                    # 检查是否有followers字段（区分空数组和无字段）
                    if 'followers' in (first_data or {}):
                        break
                    else:
                        self.logger.warning(f"首包响应中无 followers 字段, 重试: {retry_count + 1}")
                except Exception as e:
                    self.logger.warning(f"获取粉丝首包异常: {e}, 重试: {retry_count + 1}")
                    first_data = None

                if retry_count < self.JS_RETRY - 1:
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

            # 检查是否真的需要滑块验证（区分空数组和无字段）
            if 'followers' not in (first_data or {}):
                self.logger.warning("首包响应中缺少 followers 字段，可能需要滑块验证")
                self._wait_user_to_solve('获取粉丝列表（首包）')
                for retry_count in range(self.JS_RETRY):
                    self.dp.refresh()
                    try:
                        # 使用推荐的 listen.wait() API
                        pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                        first_req_url = pkt.request.url
                        first_data = self._to_json(pkt.response.body)
                        if 'followers' in (first_data or {}):
                            break
                        else:
                            self.logger.warning(f"验证后首包响应中仍无 followers 字段, 重试: {retry_count + 1}")
                    except Exception as e:
                        self.logger.warning(f"验证后获取粉丝首包异常: {e}, 重试: {retry_count + 1}")
                        first_data = None

                    if retry_count < self.JS_RETRY - 1:
                        time.sleep(self.SLEEP_BETWEEN_TRIES)

            if 'followers' not in (first_data or {}):
                raise RuntimeError("多次尝试后仍未获取到粉丝首包数据，请稍后再试。")

            # ---- 收集与去重 ----
            collected: List[Dict] = []
            seen: Set[str] = set()

            def _extend(items: List[Dict]) -> int:
                added = 0
                for it in items or []:
                    k = str(it.get('uid') or it.get('sec_uid') or it.get('unique_id') or id(it))
                    if k in seen:
                        continue
                    seen.add(k)
                    collected.append(it)
                    added += 1
                return added

            # ---- 处理首包 ----
            page_followers = first_data.get('followers') or []
            added = _extend(page_followers)
            has_more = bool(first_data.get('has_more'))
            next_max_time = int(first_data.get('min_time') or 0)
            last_min_time = next_max_time
            stuck_rounds = 0

            # 特殊处理：粉丝数为0的情况
            total_count = first_data.get('total', 0)
            if total_count == 0 and len(page_followers) == 0:
                self.logger.info(f"检测到粉丝数为0的用户，直接返回空列表")
                print(f"[首包] 粉丝数为0，无需翻页")
                return []

            print(f"[首包] items={len(page_followers)} (+{added}) "
                  f"has_more={has_more} min_time={first_data.get('min_time')} "
                  f"max_time={first_data.get('max_time')} total={total_count}")

            # ---- 翻页循环 ----
            while has_more and len(collected) < max_items:
                query_url = self._set_query_params(
                    first_req_url,
                    max_time=max(0, next_max_time),
                    count=page_count
                )

                page = self._fetch_json_via_js(query_url, scene='获取粉丝列表（翻页）')
                items = (page or {}).get('followers') or []
                page_added = _extend(items)
                has_more = bool((page or {}).get('has_more'))

                print(f"[粉丝翻页] items={len(items)} (+{page_added}) "
                      f"has_more={has_more} min_time={(page or {}).get('min_time')} "
                      f"max_time={(page or {}).get('max_time')} 粉丝数量累积={len(collected)}")
                if not has_more:
                    break

                page_min_time = int((page or {}).get('min_time') or 0)

                # 游标卡住 -> -1 纠偏；连卡两次退出
                if page_min_time >= last_min_time:
                    stuck_rounds += 1
                    next_max_time = max(0, last_min_time - 1)
                    if stuck_rounds >= 2:
                        print("[提示] 时间游标连续未推进，认为已到尽头，提前结束以避免死循环。")
                        break
                else:
                    next_max_time = page_min_time
                    last_min_time = page_min_time
                    stuck_rounds = 0

                time.sleep(self.SLEEP_BETWEEN_PAGES)
            
            follower_list_json = json.dumps(collected, ensure_ascii=False, indent=2)
            # 处理json数据,只保留特定字段
            follower_list = json.loads(follower_list_json)

            # 定义需要保留的字段（支持嵌套字段）
            keep_fields = {
                '用户UID': 'uid',
                '用户sec_uid': 'sec_uid',
                '用户抖音号': 'unique_id',
                '用户昵称': 'nickname',
                '用户签名': 'signature',
                '用户头像': 'avatar_thumb.url_list[0]',
                '粉丝数': 'follower_count',
                '关注数': 'following_count',
                '作品数': 'aweme_count',
                '获赞数': 'favoriting_count',
                '总被赞数': 'total_favorited',
                '是否官方账号': 'is_gov_media_vip',
                '是否为明星': 'is_star',
                '是否认证': 'is_verified',
            }

            def get_nested_value(data, path, default=''):
                """获取嵌套字典中的值，支持点号分隔的路径和数组索引"""
                try:
                    current = data
                    # 处理路径中的数组索引，如 avatar_thumb.url_list[0]
                    import re
                    parts = re.split(r'[\.\[\]]', path)
                    parts = [p for p in parts if p]  # 移除空字符串

                    for part in parts:
                        if part.isdigit():  # 数组索引
                            current = current[int(part)]
                        else:  # 字典键
                            current = current[part]
                    return current if current is not None else default
                except (KeyError, TypeError, AttributeError, IndexError):
                    return default

            # 只保留指定字段，创建新的清理后的列表
            cleaned_followers = []
            for follower in follower_list:
                cleaned_follower = {}
                for new_field, path in keep_fields.items():
                    value = get_nested_value(follower, path)

                    # 特殊处理：如果用户抖音号为空，使用short_id作为替代
                    if new_field == '用户抖音号' and (not value or value == ''):
                        short_id = get_nested_value(follower, 'short_id')
                        if short_id and short_id != '':
                            value = short_id

                    cleaned_follower[new_field] = value

                cleaned_followers.append(cleaned_follower)

            return cleaned_followers[:max_items]

        finally:
            self.listener_manager.pause_listener()

    def fetch_video_info(self, video_id: str) -> Dict:
        """抓取视频信息。"""
        self.logger.debug(f"开始获取视频详情 - ID: {video_id}")

        # ⭐ 性能优化：使用监听器管理器，避免每次都重新设置监听器
        # 如果之前已经设置了相同的端点，将跳过重置操作，节省1-2秒/视频
        self.listener_manager.setup_listener("/aweme/v1/web/aweme/detail/?device_platform=webapp")
        video_url = f"https://www.douyin.com/video/{video_id}"

        try:
            # 打开视频页面 → 获取首包数据
            self.logger.debug(f"访问视频页面: {video_url}")
            self.dp.get(video_url)

            # ---- 首包数据获取 ----
            data = None
            last_error = ""
            # 使用推荐的 listen.wait() API，内置超时处理和异常管理
            for retry_count in range(self.JS_RETRY):
                try:
                    pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                    response_size = len(pkt.response.body) if pkt.response.body else 0
                    self.logger.debug(f"获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                    data = self._to_json(pkt.response.body)
                    if (data or {}).get('aweme_detail'):
                        self.logger.debug(f"成功获取视频详情数据 - ID: {video_id}")
                        break
                    else:
                        self.logger.warning(f"响应中无 aweme_detail 字段 - ID: {video_id}, 重试: {retry_count + 1}")
                except Exception as e:
                    # 记录最后一次错误，用于后续错误分类
                    last_error = str(e)
                    error_type = self._classify_error(last_error, data)
                    self.logger.warning(f"获取响应包异常 - ID: {video_id}, 错误类型: {error_type}, 错误: {e}, 重试: {retry_count + 1}")

                if retry_count < self.JS_RETRY - 1:
                    delay = self._get_video_retry_delay(retry_count)
                    self.logger.debug(f"视频重试延迟: {delay:.2f}s - ID: {video_id}, 重试次数: {retry_count + 1}")
                    time.sleep(delay)

            # 改进的错误判断逻辑：区分JSON解析错误和真正的滑块验证需求
            # 只有在确实需要滑块验证时才触发人工验证
            if not ((data or {}).get('aweme_detail')):
                if self._should_trigger_captcha(data, last_error):
                    self.logger.warning(f"检测到需要滑块验证 - ID: {video_id}")
                    self._wait_user_to_solve('获取视频信息（首包）')

                    for retry_count in range(self.JS_RETRY):
                        self.dp.refresh()
                        try:
                            pkt = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
                            response_size = len(pkt.response.body) if pkt.response.body else 0
                            self.logger.debug(f"验证后获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                            data = self._to_json(pkt.response.body)
                            if (data or {}).get('aweme_detail'):
                                self.logger.info(f"验证后成功获取视频详情数据 - ID: {video_id}")
                                break
                            else:
                                self.logger.warning(f"验证后响应中仍无 aweme_detail 字段 - ID: {video_id}, 重试: {retry_count + 1}")
                        except Exception as e:
                            self.logger.warning(f"验证后获取响应包异常 - ID: {video_id}, 错误: {e}, 重试: {retry_count + 1}")

                        if retry_count < self.JS_RETRY - 1:
                            delay = self._get_video_retry_delay(retry_count)
                            self.logger.debug(f"验证后视频重试延迟: {delay:.2f}s - ID: {video_id}, 重试次数: {retry_count + 1}")
                            time.sleep(delay)
                else:
                    # 技术错误，不需要人工验证，直接记录并继续
                    error_type = self._classify_error(last_error, data)
                    self.logger.info(f"检测到技术错误({error_type})，跳过滑块验证 - ID: {video_id}")

            video_detail = (data or {}).get('aweme_detail')
            if not video_detail:
                self.logger.error(f"多次尝试后仍未获取到视频详情数据 - ID: {video_id}")
                raise RuntimeError("多次尝试后仍未获取到视频详情数据，请稍后再试。")

            # 使用统一的视频详情处理方法
            return self._process_video_detail(video_detail, video_id)

        finally:
            self.listener_manager.pause_listener()

    def _process_video_detail(self, video_detail: dict, video_id: str) -> dict:
        """
        处理视频详情数据，提取关键字段并格式化。
        使用与douyin_data.py中process_video_detail相同的字段映射，确保一致性。

        Args:
            video_detail (dict): 原始视频详情数据
            video_id (str): 视频ID

        Returns:
            dict: 格式化后的视频数据
        """
        # 导入统一的处理函数，确保字段一致性
        from douyin_data import process_video_detail
        return process_video_detail(video_detail, video_id)

    def close(self):
        """退出并关闭浏览器."""
        self.logger.info("开始关闭抓取器")
        try:
            # 清理监听器资源
            self.listener_manager.stop_listener()
            
            # 如果有正在运行的并发任务，这里可以添加清理逻辑
            # 但一般情况下，关闭浏览器就足够了
            self.dp.browser.quit()
            self.logger.info("抓取器已关闭")
        except Exception as e:
            self.logger.warning(f"关闭抓取器时出现异常: {e}")
    
    def cleanup_concurrent_resources(self):
        """清理并发相关资源（如果需要单独清理）"""
        self.logger.info("开始清理并发相关资源")
        # 注意：此方法主要用于调试或特殊情况
        # 正常情况下，并发资源会在 fetch_favorites_concurrent 的 finally 块中自动清理